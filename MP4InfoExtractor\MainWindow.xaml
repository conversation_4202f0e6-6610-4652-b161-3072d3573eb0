﻿<Window x:Class="MP4InfoExtractor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MP4InfoExtractor"
        mc:Ignorable="d"
        Title="歌曲信息提取器" Height="600" Width="900" MinWidth="800" MinHeight="500">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 文件夹选择区域 -->
        <GroupBox Grid.Row="0" Header="选择文件夹" Margin="0,0,0,10">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="txtFolderPath" Grid.Row="0" Grid.Column="0" 
                         IsReadOnly="True" Height="25" VerticalAlignment="Center"
                         Text="请选择包含MP4歌曲文件的文件夹"/>
                <Button x:Name="btnBrowse" Grid.Row="0" Grid.Column="1" 
                        Content="浏览..." Width="80" Height="25" Margin="10,0,0,0"
                        Click="BtnBrowse_Click"/>
            </Grid>
        </GroupBox>

        <!-- 操作按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <Button x:Name="btnScan" Content="开始扫描" Width="100" Height="30" 
                    Click="BtnScan_Click" IsEnabled="False"/>
            <Button x:Name="btnExport" Content="导出 CSV" Width="100" Height="30" 
                    Margin="10,0,0,0" Click="BtnExport_Click" IsEnabled="False"/>
            <TextBlock x:Name="lblStatus" VerticalAlignment="Center" Margin="20,0,0,0" 
                       Text="准备就绪" FontWeight="Bold"/>
        </StackPanel>

        <!-- 日志显示区域 -->
        <GroupBox Grid.Row="2" Header="扫描日志">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <TextBox x:Name="txtLog" IsReadOnly="True" 
                         TextWrapping="Wrap" AcceptsReturn="True"
                         Background="Black" Foreground="LightGreen"
                         FontFamily="Consolas" FontSize="12"/>
            </ScrollViewer>
        </GroupBox>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="lblStatusBar" Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="lblFileCount" Text="找到 0 个文件"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
