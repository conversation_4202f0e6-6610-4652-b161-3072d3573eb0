# MP4信息提取器

## 功能介绍

MP4信息提取器是一个专门用于从特定格式的MP4歌曲文件名中提取结构化信息的桌面应用程序。

## 支持的文件名格式

程序能够解析以下格式的MP4文件名：

```
[艺术家] - [歌曲名称] ([节目名称] 第[期数]期) - [性别/组合类型] - 现场 - [语言] - [地区] - 4k.mp4
```

### 示例文件名：
- `A-Lin - ROMADIW (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4`
- `BENI - crazy girl (歌手2025 第13期) - 女歌手 - 现场 - 日语 - 日韩 - 4k.mp4`
- `<PERSON>ler - Water (歌手2025 第13期) - 女歌手 - 现场 - 英语 - 欧美 - 4k.mp4`
- `陈楚生&郑钧 - 赤裸裸 (歌手2025 第13期) - 男歌手 - 现场 - 国语 - 大陆 - 4k.mp4`

## 提取的信息字段

程序会从文件名中提取以下信息：

1. **艺术家** - 演唱者姓名（支持合作艺术家，如 "艺术家A&艺术家B"）
2. **歌曲名称** - 歌曲的标题
3. **节目名称** - 节目的名称（如"歌手2025"）
4. **期数** - 节目的期数（如"13"）
5. **性别/组合类型** - 如"女歌手"、"男歌手"、"合唱"等
6. **语言** - 歌曲语言（如"国语"、"英语"、"日语"等）
7. **地区** - 地区标识（如"大陆"、"欧美"、"日韩"等）
8. **视频质量** - 视频质量标识（通常为"4k"）
9. **原始文件名** - 完整的原始文件名
10. **文件路径** - 文件的完整路径

## 使用方法

### 1. 选择文件夹
- 点击"浏览..."按钮
- 选择包含MP4歌曲文件的文件夹
- 程序会递归扫描该文件夹及其所有子文件夹

### 2. 开始扫描
- 选择文件夹后，"开始扫描"按钮会被启用
- 点击"开始扫描"开始分析文件
- 扫描过程中会在日志区域显示详细信息

### 3. 导出CSV
- 扫描完成后，如果找到了歌曲文件，"导出 CSV"按钮会被启用
- 点击"导出 CSV"选择保存位置
- 程序会生成包含所有歌曲信息的CSV文件

## CSV文件格式

导出的CSV文件包含以下列：

```
艺术家,歌曲名称,节目名称,期数,性别/组合类型,语言,地区,视频质量,原始文件名,文件路径
```

CSV文件采用UTF-8编码，确保中文字符正确显示。

## 特性

- **递归扫描**：自动扫描选定文件夹及其所有子文件夹
- **智能解析**：支持标准格式和简化格式的文件名解析
- **错误处理**：遇到解析失败的文件时会记录但不中断程序运行
- **异步操作**：扫描过程不会阻塞用户界面
- **日志记录**：详细的操作日志，方便调试和跟踪
- **CSV导出**：生成标准格式的CSV文件，支持特殊字符处理

## 系统要求

- Windows 7 或更高版本
- .NET Framework 4.5 或更高版本

## 技术实现

- **框架**：.NET Framework 4.5
- **界面**：WPF (Windows Presentation Foundation)
- **语言**：C#
- **解析引擎**：正则表达式 + 回退解析机制

## 注意事项

1. 如果文件名格式与预期不完全匹配，程序会尝试使用简化解析方式
2. 无法解析的文件会在日志中记录，但不会影响其他文件的处理
3. 建议在扫描大量文件时注意磁盘空间和内存使用情况
4. CSV文件会自动处理包含逗号、引号等特殊字符的字段

## 故障排除

### 常见问题

1. **扫描按钮无法点击**
   - 确保已选择有效的文件夹路径

2. **导出按钮无法点击**
   - 确保扫描已完成且找到了至少一个有效的歌曲文件

3. **解析失败**
   - 检查文件名是否符合预期格式
   - 查看日志区域的详细错误信息

4. **CSV文件乱码**
   - 确保使用支持UTF-8编码的程序打开CSV文件
   - 推荐使用Excel或专业的CSV编辑器

## 版本信息

- 版本：1.0
- 发布日期：2025年8月
- 作者：GitHub Copilot
