# 创建测试文件的PowerShell脚本

# 创建测试目录
$testDir = "d:\MP4InfoExtractor\TestFiles"
if (!(Test-Path $testDir)) {
    New-Item -ItemType Directory -Path $testDir
}

# 创建子目录
$subDir1 = "$testDir\歌手2025第13期"
$subDir2 = "$testDir\其他节目"
if (!(Test-Path $subDir1)) {
    New-Item -ItemType Directory -Path $subDir1
}
if (!(Test-Path $subDir2)) {
    New-Item -ItemType Directory -Path $subDir2
}

# 测试文件名列表（基于需求文档中的示例）
$testFiles = @(
    "A-Lin - ROMADIW (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "A-Lin&彭佳慧 - 伤痕 (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "BENI - crazy girl (歌手2025 第13期) - 女歌手 - 现场 - 日语 - 日韩 - 4k.mp4",
    "BENI&刘惜君 - 月半小夜曲 (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "<PERSON> Kinstler - Water (歌手2025 第13期) - 女歌手 - 现场 - 英语 - 欧美 - 4k.mp4",
    "Grace Kinstler&Chanté Moore - Just Like Fire (歌手2025 第13期) - 女歌手 - 现场 - 英语 - 欧美 - 4k.mp4",
    "Mickey Guyton - Better Than You Left Me (歌手2025 第13期) - 女歌手 - 现场 - 英语 - 欧美 - 4k.mp4",
    "Mickey Guyton&Loren Allred - Never Enough (歌手2025 第13期) - 女歌手 - 现场 - 英语 - 欧美 - 4k.mp4",
    "陈楚生&郑钧 - 赤裸裸 (歌手2025 第13期) - 男歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "单依纯 - 有趣 (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "单依纯&王力宏 - 落叶归根 (歌手2025 第13期) - 合唱 - 现场 - 国语 - 大陆 - 4k.mp4",
    "李佳薇 - 天堂·煎熬 (歌手2025 第13期) - 女歌手 - 现场 - 国语 - 大陆 - 4k.mp4",
    "李佳薇&孙楠 - 燃烧 (歌手2025 第13期) - 合唱 - 现场 - 国语 - 大陆 - 4k.mp4"
)

# 在主测试目录创建文件
foreach ($fileName in $testFiles) {
    $filePath = Join-Path $subDir1 $fileName
    "测试内容 - 这是一个测试MP4文件" | Out-File -FilePath $filePath -Encoding utf8
    Write-Host "创建文件: $fileName"
}

# 在子目录创建文件
foreach ($fileName in $testFiles[8..12]) {
    $filePath = Join-Path $subDir2 $fileName
    "测试内容 - 这是一个测试MP4文件" | Out-File -FilePath $filePath -Encoding utf8
    Write-Host "创建文件: $fileName"
}

# 创建一些不符合格式的文件用于测试错误处理
$invalidFiles = @(
    "不符合格式的文件.mp4",
    "艺术家 - 歌曲名称.mp4",
    "A-Lin - 歌曲名称 (不完整格式).mp4"
)

foreach ($fileName in $invalidFiles) {
    $filePath = Join-Path $testDir $fileName
    "测试内容 - 这是一个格式不正确的测试文件" | Out-File -FilePath $filePath -Encoding utf8
    Write-Host "创建不符合格式的文件: $fileName"
}

Write-Host ""
Write-Host "测试文件创建完成！"
Write-Host "测试目录: $testDir"
Write-Host "总共创建了 $($testFiles.Count + $invalidFiles.Count) 个测试文件"
