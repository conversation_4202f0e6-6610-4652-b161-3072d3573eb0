using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace MP4InfoExtractor
{
    /// <summary>
    /// 中文拼音转换工具类
    /// </summary>
    public static class PinyinHelper
    {
        /// <summary>
        /// 获取中文字符的拼音首字母
        /// </summary>
        public static string GetFirstLetter(char c)
        {
            // 使用简化的拼音映射表（覆盖常用汉字）
            var pinyinMap = GetPinyinMap();
            
            if (pinyinMap.ContainsKey(c))
            {
                return pinyinMap[c].Substring(0, 1).ToUpper();
            }

            // 如果没有找到精确映射，使用Unicode范围估算
            return GetFirstLetterByUnicode(c);
        }

        /// <summary>
        /// 获取中文字符的完整拼音
        /// </summary>
        public static string GetFullPinyin(char c)
        {
            var pinyinMap = GetPinyinMap();
            
            if (pinyinMap.ContainsKey(c))
            {
                return pinyinMap[c];
            }

            // 如果没有找到精确映射，返回空字符串
            return "";
        }

        /// <summary>
        /// 获取字符串的拼音首字母缩写
        /// </summary>
        public static string GetFirstLetters(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            var result = new StringBuilder();
            foreach (char c in text)
            {
                if (IsChinese(c))
                {
                    result.Append(GetFirstLetter(c));
                }
                else if (char.IsLetter(c))
                {
                    result.Append(char.ToUpper(c));
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 获取字符串的完整拼音
        /// </summary>
        public static string GetFullPinyin(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            var result = new StringBuilder();
            foreach (char c in text)
            {
                if (IsChinese(c))
                {
                    var pinyin = GetFullPinyin(c);
                    if (!string.IsNullOrEmpty(pinyin))
                    {
                        result.Append(pinyin);
                        result.Append(" ");
                    }
                }
                else if (char.IsLetter(c))
                {
                    result.Append(c);
                }
                else if (char.IsWhiteSpace(c))
                {
                    result.Append(" ");
                }
            }

            return result.ToString().Trim();
        }

        /// <summary>
        /// 判断字符是否为中文
        /// </summary>
        public static bool IsChinese(char c)
        {
            return c >= 0x4e00 && c <= 0x9fff;
        }

        /// <summary>
        /// 通过Unicode范围估算拼音首字母
        /// </summary>
        private static string GetFirstLetterByUnicode(char c)
        {
            int code = (int)c;
            
            if (code >= 0x4e00 && code <= 0x4fff) return "A"; 
            if (code >= 0x5000 && code <= 0x51ff) return "B"; 
            if (code >= 0x5200 && code <= 0x53ff) return "C"; 
            if (code >= 0x5400 && code <= 0x55ff) return "D"; 
            if (code >= 0x5600 && code <= 0x57ff) return "F"; 
            if (code >= 0x5800 && code <= 0x59ff) return "G"; 
            if (code >= 0x5a00 && code <= 0x5bff) return "H"; 
            if (code >= 0x5c00 && code <= 0x5dff) return "J"; 
            if (code >= 0x5e00 && code <= 0x5fff) return "K"; 
            if (code >= 0x6000 && code <= 0x61ff) return "L"; 
            if (code >= 0x6200 && code <= 0x63ff) return "M"; 
            if (code >= 0x6400 && code <= 0x65ff) return "N"; 
            if (code >= 0x6600 && code <= 0x67ff) return "P"; 
            if (code >= 0x6800 && code <= 0x69ff) return "Q"; 
            if (code >= 0x6a00 && code <= 0x6bff) return "R"; 
            if (code >= 0x6c00 && code <= 0x6dff) return "S"; 
            if (code >= 0x6e00 && code <= 0x6fff) return "T"; 
            if (code >= 0x7000 && code <= 0x71ff) return "W"; 
            if (code >= 0x7200 && code <= 0x73ff) return "X"; 
            if (code >= 0x7400 && code <= 0x75ff) return "Y"; 
            if (code >= 0x7600 && code <= 0x9fff) return "Z"; 
            
            return "";
        }

        /// <summary>
        /// 获取拼音映射表
        /// </summary>
        private static Dictionary<char, string> GetPinyinMap()
        {
            return new Dictionary<char, string>
            {
                // 常用字拼音映射
                {'我', "wo"}, {'爱', "ai"}, {'你', "ni"}, {'的', "de"}, {'是', "shi"}, {'不', "bu"}, {'在', "zai"}, {'人', "ren"}, {'有', "you"}, {'他', "ta"},
                {'这', "zhe"}, {'中', "zhong"}, {'大', "da"}, {'为', "wei"}, {'上', "shang"}, {'个', "ge"}, {'国', "guo"}, {'一', "yi"}, {'二', "er"}, {'三', "san"},
                {'四', "si"}, {'五', "wu"}, {'六', "liu"}, {'七', "qi"}, {'八', "ba"}, {'九', "jiu"}, {'十', "shi"}, {'来', "lai"}, {'到', "dao"}, {'说', "shuo"},
                {'时', "shi"}, {'年', "nian"}, {'月', "yue"}, {'日', "ri"}, {'好', "hao"}, {'对', "dui"}, {'就', "jiu"}, {'和', "he"}, {'要', "yao"}, {'会', "hui"},
                {'多', "duo"}, {'很', "hen"}, {'可', "ke"}, {'小', "xiao"}, {'看', "kan"}, {'听', "ting"}, {'想', "xiang"}, {'知', "zhi"}, {'道', "dao"}, {'天', "tian"},
                {'地', "di"}, {'山', "shan"}, {'水', "shui"}, {'火', "huo"}, {'风', "feng"}, {'雨', "yu"}, {'雪', "xue"}, {'花', "hua"}, {'草', "cao"}, {'树', "shu"},
                {'鸟', "niao"}, {'鱼', "yu"}, {'猫', "mao"}, {'狗', "gou"}, {'牛', "niu"}, {'马', "ma"}, {'羊', "yang"}, {'猪', "zhu"}, {'鸡', "ji"}, {'鸭', "ya"},
                {'书', "shu"}, {'本', "ben"}, {'字', "zi"}, {'词', "ci"}, {'句', "ju"}, {'文', "wen"}, {'章', "zhang"}, {'诗', "shi"}, {'歌', "ge"}, {'音', "yin"},
                {'乐', "le"}, {'唱', "chang"}, {'跳', "tiao"}, {'舞', "wu"}, {'画', "hua"}, {'写', "xie"}, {'读', "du"}, {'学', "xue"}, {'习', "xi"}, {'工', "gong"},
                {'作', "zuo"}, {'生', "sheng"}, {'活', "huo"}, {'家', "jia"}, {'房', "fang"}, {'门', "men"}, {'窗', "chuang"}, {'桌', "zhuo"}, {'椅', "yi"}, {'床', "chuang"},
                {'吃', "chi"}, {'喝', "he"}, {'穿', "chuan"}, {'住', "zhu"}, {'行', "xing"}, {'走', "zou"}, {'跑', "pao"}, {'跳', "tiao"}, {'站', "zhan"}, {'坐', "zuo"},
                {'睡', "shui"}, {'醒', "xing"}, {'笑', "xiao"}, {'哭', "ku"}, {'生', "sheng"}, {'死', "si"}, {'老', "lao"}, {'少', "shao"}, {'男', "nan"}, {'女', "nv"},
                {'父', "fu"}, {'母', "mu"}, {'子', "zi"}, {'女', "nv"}, {'兄', "xiong"}, {'弟', "di"}, {'姐', "jie"}, {'妹', "mei"}, {'夫', "fu"}, {'妻', "qi"},
                {'红', "hong"}, {'黄', "huang"}, {'蓝', "lan"}, {'绿', "lv"}, {'黑', "hei"}, {'白', "bai"}, {'灰', "hui"}, {'紫', "zi"}, {'粉', "fen"}, {'棕', "zong"},
                {'大', "da"}, {'小', "xiao"}, {'长', "chang"}, {'短', "duan"}, {'高', "gao"}, {'低', "di"}, {'胖', "pang"}, {'瘦', "shou"}, {'厚', "hou"}, {'薄', "bao"},
                {'新', "xin"}, {'旧', "jiu"}, {'快', "kuai"}, {'慢', "man"}, {'早', "zao"}, {'晚', "wan"}, {'冷', "leng"}, {'热', "re"}, {'温', "wen"}, {'凉', "liang"},
                {'甜', "tian"}, {'酸', "suan"}, {'苦', "ku"}, {'辣', "la"}, {'咸', "xian"}, {'香', "xiang"}, {'臭', "chou"}, {'美', "mei"}, {'丑', "chou"}, {'净', "jing"},
                {'脏', "zang"}, {'亮', "liang"}, {'暗', "an"}, {'静', "jing"}, {'闹', "nao"}, {'忙', "mang"}, {'闲', "xian"}, {'累', "lei"}, {'轻', "qing"}, {'重', "zhong"},
                // 测试文件中的字
                {'半', "ban"}, {'夜', "ye"}, {'曲', "qu"}, {'赤', "chi"}, {'裸', "luo"}, {'有', "you"}, {'趣', "qu"}, {'落', "luo"}, {'叶', "ye"},
                {'归', "gui"}, {'根', "gen"}, {'天', "tian"}, {'堂', "tang"}, {'煎', "jian"}, {'熬', "ao"}, {'燃', "ran"}, {'烧', "shao"}, {'伤', "shang"}, {'痕', "hen"},
                {'疯', "feng"}, {'狂', "kuang"}, {'骚', "sao"}, {'每', "mei"}, {'当', "dang"}, {'依', "yi"}, {'纯', "chun"}, {'佳', "jia"}, {'薇', "wei"}, 
                {'王', "wang"}, {'力', "li"}, {'宏', "hong"}, {'孙', "sun"}, {'楠', "nan"}, {'郑', "zheng"}, {'钧', "jun"}, {'陈', "chen"}, {'楚', "chu"}, {'单', "dan"}
            };
        }
    }
}
