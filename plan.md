# MP4InfoExtractor Project - .gitignore Plan

## Project Analysis

Based on the project structure analysis, this is a C# WPF application with:
- Visual Studio solution and project files
- bin/ and obj/ directories for build outputs
- .vs/ directory for Visual Studio temporary files
- Release package directory
- Test files directory

## Recommended .gitignore Content

```gitignore
# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Visual Studio 2015/2017/2019/2022 cache/options
*.vcxproj.filters
*.vcxproj.user
*.vcxproj.filters.user

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio 2015+ cache/options
*.vspscc
*.vssscc
*.scc
*.opensdf
*.sdf
*.deps.json
*.runtimeconfig.json

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml

# Build Results of an ATL Project
[Dd]ebug*/
[Rr]elease*/
[Rr]elease*/
[Oo]bj*/
[Bb]in/*
/[Dd]ebug*/
/[Rr]elease*/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
*.pidb
*.svclog
*.scc

# Chutzpah test files
_Chutzpah*

# Visual C++ build folder
[Vv]cx[Pp]roj.[Ff]ilters

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# TFS 2012 files
*.tfil
*.tfs*

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity
_TeamCity*

# DotCover
*.dotCover

# AxoCover
*.axoCover/*
!.axoCover/settings.json

# Coverlet
coverage.json
coverage.xml
coverage.info

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings
PublishScripts/
ServiceDefinition.csdef
ServiceConfiguration.cscfg
Package/*
!*.pubxml

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/packages/*
# except build/, which is used as an MSBuild target.
!**/packages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/packages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# Including the obj folder compromises the build
!**/obj/
**/obj/*

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting
# Windows Forms designer's settings file to
