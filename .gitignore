# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
[Oo]ut/
[Ll]og/

# Visual Studio files
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio 2015/2017 cache/options directory
.vs/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# DNX
project.lock.json
artifacts/

# Visual Studio 2017 files
*.dbmdl
*.pubxml
*.pubxml.user
*.pfx
*.p12

# Visual Studio 2019 files
*.njsproj
*.bsproj

# Visual Studio 2022 files
*.vstemplate

# ReSharper
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity
_TeamCity*

# DotCover
*.dotCover

# AxoCover
*.axoCover/*
!.axoCover/settings.json

# Coverlet
coverage.json
coverage.xml
coverage.info

# Visual Studio for Mac
*.csproj*.user

# Windows Azure Build
csx/
*.build.csdef

# Windows Store
AppPackages/
BundleArtifacts/

# Visual Studio tools for CMake
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles/

# FxCop
*.FxCop

# NCover
*.ncover
*.ncover.xml
CoverageReport*

# SQL Server files
*.mdf
*.ldf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser

# Microsoft Fakes
FakesAssemblies/

# GhostDoc
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# GitHub
*.md
*.yml

# CodeRush
*.cr/

# Python Tools for Visual Studio
*.pyc
__pycache__/

# Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

# Tabs Studio
*.tss

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# OpenCover
results.xml
results*.xml

# Azure Stream Analytics
ASALocalRun/
ASALocalRunConfig.json

# MSBuild
*.dbproj.schemaview

# .NET Core
project.lock.json
*.dbproj.schemaview
*.mfrproj.schemaview

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file
*.[Oo]ld
*.rdz

# SQL Server files
*.mdf
*.ldf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings

# Microsoft Fakes
FakesAssemblies/

# Node.js
node_modules/
npm-debug.log*

# TypeScript
*.tsbuildinfo

# VS Code
.vscode/

# Unity
/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/Assets/AssetStoreTools*

# Gradle
.gradle/

# Maven
target/

# Keystore files
*.jks
*.keystore

# Log files
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# End of file